
import 'package:flutter/material.dart';
import 'package:blockway/components/board/main_board.dart';

class GameplayScreen extends StatelessWidget {
  const GameplayScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF2E3440),
      appBar: AppBar(
        backgroundColor: const Color(0xFF3B4252),
        foregroundColor: Colors.white,
        title: const Text('Blockway!'),
      ),
      body: const Center(
        child: MainBoard(gridSize: 5),
      ),
    );
  }
}

