
import 'package:flutter/material.dart';

class GameGrid extends StatelessWidget {
  final int gridSize;
  final double cellSize;
  final Color gridColor;
  final Color backgroundColor;

  const GameGrid({
    super.key,
    this.gridSize = 4,
    this.cellSize = 40.0,
    this.gridColor = Colors.white,
    this.backgroundColor = Colors.transparent,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: gridSize * cellSize,
      height: gridSize * cellSize,
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(color: gridColor, width: 2),
      ),
      child: GridView.count(
        crossAxisCount: gridSize,
        physics: const NeverScrollableScrollPhysics(),
        children: List.generate(gridSize * gridSize, (index) {
          return Container(
            decoration: BoxDecoration(
              border: Border.all(color: gridColor, width: 1),
            ),
          );
        }),
      ),
    );
  }
}

