
import 'package:flutter/material.dart';
import 'package:blockway/components/board/grid.dart';
import 'package:blockway/components/blocks/main_block.dart';

class MainBoard extends StatelessWidget {
  final int gridSize;

  const MainBoard({
    super.key,
    this.gridSize = 3,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Stack(
        children: [
          GameGrid(
            gridSize: gridSize,
            cellSize: 80.0,
            gridColor: Colors.white,
            backgroundColor: const Color(0xFF3B4252),
          ),
          Positioned(
            left: 2,
            top: 2,
            child: MainBlock(
              size: 76.0,
              onTap: () {
                // Handle block tap
              },
            ),
          ),
        ],
      ),
    );
  }
}

