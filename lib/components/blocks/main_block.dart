import 'package:flutter/material.dart';

class MainBlock extends StatelessWidget {
  final double size;
  final VoidCallback? onTap;

  const MainBlock({
    super.key,
    this.size = 80.0,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/blocks/main_block.png'),
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }
}